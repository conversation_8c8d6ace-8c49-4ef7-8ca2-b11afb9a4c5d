<variant
    name="release"
    package="io.flutter.plugins.sharedpreferences"
    minSdkVersion="21"
    targetSdkVersion="21"
    mergedManifest="D:\workspaces\nsl\back\srsrmain\frontend\build\shared_preferences_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml"
    manifestMergeReport="D:\workspaces\nsl\back\srsrmain\frontend\build\shared_preferences_android\outputs\logs\manifest-merger-release-report.txt"
    proguardFiles="D:\workspaces\nsl\back\srsrmain\frontend\build\shared_preferences_android\intermediates\default_proguard_files\global\proguard-android.txt-8.7.3"
    partialResultsDir="D:\workspaces\nsl\back\srsrmain\frontend\build\shared_preferences_android\intermediates\lint_partial_results\release\lintAnalyzeRelease\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\main\kotlin;src\release\java;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="D:\workspaces\nsl\back\srsrmain\frontend\build\shared_preferences_android\intermediates\javac\release\compileReleaseJavaWithJavac\classes;D:\workspaces\nsl\back\srsrmain\frontend\build\shared_preferences_android\tmp\kotlin-classes\release;D:\workspaces\nsl\back\srsrmain\frontend\build\shared_preferences_android\intermediates\compile_r_class_jar\release\generateReleaseRFile\R.jar"
      type="MAIN"
      applicationId="io.flutter.plugins.sharedpreferences"
      generatedSourceFolders="D:\workspaces\nsl\back\srsrmain\frontend\build\shared_preferences_android\generated\ap_generated_sources\release\out"
      generatedResourceFolders="D:\workspaces\nsl\back\srsrmain\frontend\build\shared_preferences_android\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.12\transforms\7ff7c222f72a39cd2d6660f4156e5aff\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
