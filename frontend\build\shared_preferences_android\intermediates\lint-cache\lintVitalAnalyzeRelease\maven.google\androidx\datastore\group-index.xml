<?xml version='1.0' encoding='UTF-8'?>
<androidx.datastore>
  <datastore versions="1.0.0-alpha03,1.0.0-alpha04,1.0.0-alpha05,1.0.0-alpha06,1.0.0-alpha07,1.0.0-alpha08,1.0.0-beta01,1.0.0-beta02,1.0.0-rc01,1.0.0-rc02,1.0.0,1.1.0-dev01,1.1.0-alpha01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-android versions="1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core versions="1.0.0-alpha01,1.0.0-alpha02,1.0.0-alpha03,1.0.0-alpha04,1.0.0-alpha05,1.0.0-alpha06,1.0.0-alpha07,1.0.0-alpha08,1.0.0-beta01,1.0.0-beta02,1.0.0-rc01,1.0.0-rc02,1.0.0,1.1.0-dev01,1.1.0-alpha01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-android versions="1.1.0-alpha01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-iosarm64 versions="1.1.0-dev01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-iossimulatorarm64 versions="1.1.0-dev01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-iosx64 versions="1.1.0-dev01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-jvm versions="1.1.0-dev01,1.1.0-alpha01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-linuxarm64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-linuxx64 versions="1.1.0-dev01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-macosarm64 versions="1.1.0-dev01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-macosx64 versions="1.1.0-dev01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-okio versions="1.1.0-dev01,1.1.0-alpha01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-okio-iosarm64 versions="1.1.0-dev01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-okio-iossimulatorarm64 versions="1.1.0-dev01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-okio-iosx64 versions="1.1.0-dev01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-okio-jvm versions="1.1.0-dev01,1.1.0-alpha01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-okio-linuxarm64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-okio-linuxx64 versions="1.1.0-dev01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-okio-macosarm64 versions="1.1.0-dev01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-okio-macosx64 versions="1.1.0-dev01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-okio-tvosarm64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-okio-tvossimulatorarm64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-okio-tvosx64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-okio-watchosarm32 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-okio-watchosarm64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-okio-watchosdevicearm64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-okio-watchossimulatorarm64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-okio-watchosx64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-tvosarm64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-tvossimulatorarm64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-tvosx64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-watchosarm32 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-watchosarm64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-watchosdevicearm64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-watchossimulatorarm64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-core-watchosx64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-guava versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-iosarm64 versions="1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-iossimulatorarm64 versions="1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-iosx64 versions="1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-jvm versions="1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-linuxarm64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-linuxx64 versions="1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-macosarm64 versions="1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-macosx64 versions="1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences versions="1.0.0-alpha01,1.0.0-alpha02,1.0.0-alpha03,1.0.0-alpha04,1.0.0-alpha05,1.0.0-alpha06,1.0.0-alpha07,1.0.0-alpha08,1.0.0-beta01,1.0.0-beta02,1.0.0-rc01,1.0.0-rc02,1.0.0,1.1.0-dev01,1.1.0-alpha01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-android versions="1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-core versions="1.0.0-alpha03,1.0.0-alpha04,1.0.0-alpha05,1.0.0-alpha06,1.0.0-alpha07,1.0.0-alpha08,1.0.0-beta01,1.0.0-beta02,1.0.0-rc01,1.0.0-rc02,1.0.0,1.1.0-dev01,1.1.0-alpha01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-core-android versions="1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-core-iosarm64 versions="1.1.0-dev01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-core-iossimulatorarm64 versions="1.1.0-dev01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-core-iosx64 versions="1.1.0-dev01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-core-jvm versions="1.1.0-dev01,1.1.0-alpha01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-core-linuxarm64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-core-linuxx64 versions="1.1.0-dev01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-core-macosarm64 versions="1.1.0-dev01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-core-macosx64 versions="1.1.0-dev01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-core-tvosarm64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-core-tvossimulatorarm64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-core-tvosx64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-core-watchosarm32 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-core-watchosarm64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-core-watchosdevicearm64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-core-watchossimulatorarm64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-core-watchosx64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-external-protobuf versions="1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-iosarm64 versions="1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-iossimulatorarm64 versions="1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-iosx64 versions="1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-jvm versions="1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-linuxarm64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-linuxx64 versions="1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-macosarm64 versions="1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-macosx64 versions="1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-proto versions="1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-rxjava2 versions="1.0.0-alpha06,1.0.0-alpha07,1.0.0-alpha08,1.0.0-beta01,1.0.0-beta02,1.0.0-rc01,1.0.0-rc02,1.0.0,1.1.0-dev01,1.1.0-alpha01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-rxjava3 versions="1.0.0-alpha06,1.0.0-alpha07,1.0.0-alpha08,1.0.0-beta01,1.0.0-beta02,1.0.0-rc01,1.0.0-rc02,1.0.0,1.1.0-dev01,1.1.0-alpha01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-tvosarm64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-tvossimulatorarm64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-tvosx64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-watchosarm32 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-watchosarm64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-watchosdevicearm64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-watchossimulatorarm64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-preferences-watchosx64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-rxjava2 versions="1.0.0-alpha06,1.0.0-alpha07,1.0.0-alpha08,1.0.0-beta01,1.0.0-beta02,1.0.0-rc01,1.0.0-rc02,1.0.0,1.1.0-dev01,1.1.0-alpha01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-rxjava3 versions="1.0.0-alpha06,1.0.0-alpha07,1.0.0-alpha08,1.0.0-beta01,1.0.0-beta02,1.0.0-rc01,1.0.0-rc02,1.0.0,1.1.0-dev01,1.1.0-alpha01,1.1.0-alpha02,1.1.0-alpha03,1.1.0-alpha04,1.1.0-alpha05,1.1.0-alpha06,1.1.0-alpha07,1.1.0-beta01,1.1.0-beta02,1.1.0-rc01,1.1.0,1.1.1,1.1.2,1.1.3,1.1.4,1.1.5,1.1.6,1.1.7,1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-tvosarm64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-tvossimulatorarm64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-tvosx64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-watchosarm32 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-watchosarm64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-watchosdevicearm64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-watchossimulatorarm64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
  <datastore-watchosx64 versions="1.2.0-alpha01,1.2.0-alpha02"/>
</androidx.datastore>
