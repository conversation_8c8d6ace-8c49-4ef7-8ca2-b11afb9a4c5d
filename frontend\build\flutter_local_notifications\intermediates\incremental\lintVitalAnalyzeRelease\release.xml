<variant
    name="release"
    package="com.dexterous.flutterlocalnotifications"
    minSdkVersion="16"
    targetSdkVersion="16"
    mergedManifest="D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_local_notifications\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml"
    proguardFiles="D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_local_notifications\intermediates\default_proguard_files\global\proguard-android.txt-8.7.3"
    partialResultsDir="D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_local_notifications\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.12\transforms\e978e1d83898a21c6708d0478084b105\transformed\desugar_jdk_libs_configuration-1.2.2-desugar-lint.txt;C:\Users\<USER>\.gradle\caches\8.12\transforms\********************************\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      coreLibraryDesugaring="true"
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_local_notifications\intermediates\javac\release\compileReleaseJavaWithJavac\classes;D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_local_notifications\intermediates\compile_r_class_jar\release\generateReleaseRFile\R.jar"
      type="MAIN"
      applicationId="com.dexterous.flutterlocalnotifications"
      generatedSourceFolders="D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_local_notifications\generated\ap_generated_sources\release\out"
      generatedResourceFolders="D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_local_notifications\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.12\transforms\e978e1d83898a21c6708d0478084b105\transformed\desugar_jdk_libs_configuration-1.2.2-desugar-lint.txt;C:\Users\<USER>\.gradle\caches\8.12\transforms\********************************\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
