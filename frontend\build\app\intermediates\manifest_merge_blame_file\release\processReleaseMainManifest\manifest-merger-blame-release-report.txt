1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.srsr.property.srsr_property_management"
4    android:versionCode="1"
5    android:versionName="1.0.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10    <!--
11         Required to query activities that can process text, see:
12         https://developer.android.com/training/package-visibility and
13         https://developer.android.com/reference/android/content/Intent#ACTION_PROCESS_TEXT.
14
15         In particular, this is used by the Flutter engine in io.flutter.plugin.text.ProcessTextPlugin.
16    -->
17    <queries>
17-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:39:5-44:15
18        <intent>
18-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:40:9-43:18
19            <action android:name="android.intent.action.PROCESS_TEXT" />
19-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:41:13-72
19-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:41:21-70
20
21            <data android:mimeType="text/plain" />
21-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:42:13-50
21-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:42:19-48
22        </intent>
23        <intent>
23-->[:file_picker] D:\workspaces\nsl\back\srsrmain\frontend\build\file_picker\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-12:18
24            <action android:name="android.intent.action.GET_CONTENT" />
24-->[:file_picker] D:\workspaces\nsl\back\srsrmain\frontend\build\file_picker\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-72
24-->[:file_picker] D:\workspaces\nsl\back\srsrmain\frontend\build\file_picker\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:21-69
25
26            <data android:mimeType="*/*" />
26-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:42:13-50
26-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:42:19-48
27        </intent> <!-- Needs to be explicitly declared on Android R+ -->
28        <package android:name="com.google.android.apps.maps" />
28-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:9-64
28-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:33:18-61
29    </queries>
30
31    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
31-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-77
31-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-74
32    <uses-permission android:name="android.permission.RECEIVE_BOOT_COMPLETED" />
32-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-81
32-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:22-78
33    <uses-permission android:name="android.permission.WAKE_LOCK" />
33-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:5-68
33-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:22-65
34    <uses-permission android:name="android.permission.VIBRATE" />
34-->[:flutter_local_notifications] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_local_notifications\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-66
34-->[:flutter_local_notifications] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_local_notifications\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-63
35    <uses-permission android:name="android.permission.POST_NOTIFICATIONS" />
35-->[:flutter_local_notifications] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_local_notifications\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:5-77
35-->[:flutter_local_notifications] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_local_notifications\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:22-74
36    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
36-->[:connectivity_plus] D:\workspaces\nsl\back\srsrmain\frontend\build\connectivity_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:5-79
36-->[:connectivity_plus] D:\workspaces\nsl\back\srsrmain\frontend\build\connectivity_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:7:22-76
37    <uses-permission android:name="android.permission.INTERNET" />
37-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:24:5-67
37-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:24:22-64
38
39    <uses-feature
39-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:26:5-28:35
40        android:glEsVersion="0x00020000"
40-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:27:9-41
41        android:required="true" />
41-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:28:9-32
42
43    <permission
43-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
44        android:name="com.srsr.property.srsr_property_management.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
44-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
45        android:protectionLevel="signature" />
45-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
46
47    <uses-permission android:name="com.srsr.property.srsr_property_management.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
47-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
48
49    <application
50        android:name="android.app.Application"
50-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:4:9-42
51        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
51-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\66aa7f682cf61ffe3ee75db6ee238d77\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
52        android:extractNativeLibs="false"
53        android:icon="@mipmap/ic_launcher"
53-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:5:9-43
54        android:label="srsr_property_management" >
54-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:3:9-49
55        <activity
55-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:6:9-27:20
56            android:name="com.srsr.property.srsr_property_management.MainActivity"
56-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:7:13-41
57            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|smallestScreenSize|locale|layoutDirection|fontScale|screenLayout|density|uiMode"
57-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:12:13-163
58            android:exported="true"
58-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:8:13-36
59            android:hardwareAccelerated="true"
59-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:13:13-47
60            android:launchMode="singleTop"
60-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:9:13-43
61            android:taskAffinity=""
61-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:10:13-36
62            android:theme="@style/LaunchTheme"
62-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:11:13-47
63            android:windowSoftInputMode="adjustResize" >
63-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:14:13-55
64
65            <!--
66                 Specifies an Android theme to apply to this Activity as soon as
67                 the Android process has started. This theme is visible to the user
68                 while the Flutter UI initializes. After that, this theme continues
69                 to determine the Window background behind the Flutter UI.
70            -->
71            <meta-data
71-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:19:13-22:17
72                android:name="io.flutter.embedding.android.NormalTheme"
72-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:20:15-70
73                android:resource="@style/NormalTheme" />
73-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:21:15-52
74
75            <intent-filter>
75-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:23:13-26:29
76                <action android:name="android.intent.action.MAIN" />
76-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:24:17-68
76-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:24:25-66
77
78                <category android:name="android.intent.category.LAUNCHER" />
78-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:25:17-76
78-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:25:27-74
79            </intent-filter>
80        </activity>
81        <!--
82             Don't delete the meta-data below.
83             This is used by the Flutter tool to generate GeneratedPluginRegistrant.java
84        -->
85        <meta-data
85-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:30:9-32:33
86            android:name="flutterEmbedding"
86-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:31:13-44
87            android:value="2" />
87-->D:\workspaces\nsl\back\srsrmain\frontend\android\app\src\main\AndroidManifest.xml:32:13-30
88
89        <service
89-->[:geolocator_android] D:\workspaces\nsl\back\srsrmain\frontend\build\geolocator_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-12:56
90            android:name="com.baseflow.geolocator.GeolocatorLocationService"
90-->[:geolocator_android] D:\workspaces\nsl\back\srsrmain\frontend\build\geolocator_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-77
91            android:enabled="true"
91-->[:geolocator_android] D:\workspaces\nsl\back\srsrmain\frontend\build\geolocator_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-35
92            android:exported="false"
92-->[:geolocator_android] D:\workspaces\nsl\back\srsrmain\frontend\build\geolocator_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-37
93            android:foregroundServiceType="location" />
93-->[:geolocator_android] D:\workspaces\nsl\back\srsrmain\frontend\build\geolocator_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-53
94        <service
94-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:9-16:44
95            android:name="id.flutter.flutter_background_service.BackgroundService"
95-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-83
96            android:enabled="true"
96-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-35
97            android:exported="true"
97-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:13-36
98            android:stopWithTask="false" />
98-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:13-41
99
100        <receiver
100-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:18:9-21:39
101            android:name="id.flutter.flutter_background_service.WatchdogReceiver"
101-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:13-82
102            android:enabled="true"
102-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-35
103            android:exported="true" />
103-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-36
104        <receiver
104-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:9-31:20
105            android:name="id.flutter.flutter_background_service.BootReceiver"
105-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:23:13-78
106            android:enabled="true"
106-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-35
107            android:exported="true" >
107-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:13-36
108            <intent-filter>
108-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:26:13-30:29
109                <action android:name="android.intent.action.BOOT_COMPLETED" />
109-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:17-79
109-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:27:25-76
110                <action android:name="android.intent.action.QUICKBOOT_POWERON" />
110-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:17-82
110-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:25-79
111                <action android:name="android.intent.action.MY_PACKAGE_REPLACED" />
111-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:17-84
111-->[:flutter_background_service_android] D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:25-81
112            </intent-filter>
113        </receiver>
114
115        <provider
115-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:9-17:20
116            android:name="io.flutter.plugins.imagepicker.ImagePickerFileProvider"
116-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-82
117            android:authorities="com.srsr.property.srsr_property_management.flutter.image_provider"
117-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-74
118            android:exported="false"
118-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:12:13-37
119            android:grantUriPermissions="true" >
119-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:13:13-47
120            <meta-data
120-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:14:13-16:75
121                android:name="android.support.FILE_PROVIDER_PATHS"
121-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:15:17-67
122                android:resource="@xml/flutter_image_picker_file_paths" />
122-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:16:17-72
123        </provider> <!-- Trigger Google Play services to install the backported photo picker module. -->
124        <service
124-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:19:9-31:19
125            android:name="com.google.android.gms.metadata.ModuleDependencies"
125-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:20:13-78
126            android:enabled="false"
126-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:21:13-36
127            android:exported="false" >
127-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:22:13-37
128            <intent-filter>
128-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:24:13-26:29
129                <action android:name="com.google.android.gms.metadata.MODULE_DEPENDENCIES" />
129-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:17-94
129-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:25:25-91
130            </intent-filter>
131
132            <meta-data
132-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:28:13-30:36
133                android:name="photopicker_activity:0:required"
133-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:29:17-63
134                android:value="" />
134-->[:image_picker_android] D:\workspaces\nsl\back\srsrmain\frontend\build\image_picker_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:30:17-33
135        </service>
136
137        <activity
137-->[:url_launcher_android] D:\workspaces\nsl\back\srsrmain\frontend\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:8:9-11:74
138            android:name="io.flutter.plugins.urllauncher.WebViewActivity"
138-->[:url_launcher_android] D:\workspaces\nsl\back\srsrmain\frontend\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:9:13-74
139            android:exported="false"
139-->[:url_launcher_android] D:\workspaces\nsl\back\srsrmain\frontend\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:10:13-37
140            android:theme="@android:style/Theme.NoTitleBar.Fullscreen" /> <!-- Needs to be explicitly declared on P+ -->
140-->[:url_launcher_android] D:\workspaces\nsl\back\srsrmain\frontend\build\url_launcher_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml:11:13-71
141        <uses-library
141-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:39:9-41:40
142            android:name="org.apache.http.legacy"
142-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:40:13-50
143            android:required="false" />
143-->[com.google.android.gms:play-services-maps:18.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\267285d13dfee259da936810495645c5\transformed\jetified-play-services-maps-18.2.0\AndroidManifest.xml:41:13-37
144
145        <activity
145-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3d296532cc5acbfa6d00cce05e38839\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:9-22:45
146            android:name="com.google.android.gms.common.api.GoogleApiActivity"
146-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3d296532cc5acbfa6d00cce05e38839\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:20:19-85
147            android:exported="false"
147-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3d296532cc5acbfa6d00cce05e38839\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:22:19-43
148            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
148-->[com.google.android.gms:play-services-base:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\a3d296532cc5acbfa6d00cce05e38839\transformed\jetified-play-services-base-18.3.0\AndroidManifest.xml:21:19-78
149
150        <meta-data
150-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:21:9-23:69
151            android:name="com.google.android.gms.version"
151-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:22:13-58
152            android:value="@integer/google_play_services_version" />
152-->[com.google.android.gms:play-services-basement:18.3.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\9d4e1de4e870e893108c546e2600c23f\transformed\jetified-play-services-basement-18.3.0\AndroidManifest.xml:23:13-66
153
154        <uses-library
154-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:23:9-25:40
155            android:name="androidx.window.extensions"
155-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:24:13-54
156            android:required="false" />
156-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:25:13-37
157        <uses-library
157-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:26:9-28:40
158            android:name="androidx.window.sidecar"
158-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:27:13-51
159            android:required="false" />
159-->[androidx.window:window:1.2.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\197f12b192a3f06912c946d4cbd2dd7d\transformed\jetified-window-1.2.0\AndroidManifest.xml:28:13-37
160
161        <provider
161-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:24:9-32:20
162            android:name="androidx.startup.InitializationProvider"
162-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:25:13-67
163            android:authorities="com.srsr.property.srsr_property_management.androidx-startup"
163-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:26:13-68
164            android:exported="false" >
164-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:27:13-37
165            <meta-data
165-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:29:13-31:52
166                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
166-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:30:17-78
167                android:value="androidx.startup" />
167-->[androidx.lifecycle:lifecycle-process:2.7.0] C:\Users\<USER>\.gradle\caches\8.12\transforms\0c7cd1674da718ccee593f79cf8da244\transformed\jetified-lifecycle-process-2.7.0\AndroidManifest.xml:31:17-49
168            <meta-data
168-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
169                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
169-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
170                android:value="androidx.startup" />
170-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
171        </provider>
172
173        <receiver
173-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
174            android:name="androidx.profileinstaller.ProfileInstallReceiver"
174-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
175            android:directBootAware="false"
175-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
176            android:enabled="true"
176-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
177            android:exported="true"
177-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
178            android:permission="android.permission.DUMP" >
178-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
179            <intent-filter>
179-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
180                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
180-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
180-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
181            </intent-filter>
182            <intent-filter>
182-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
183                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
183-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
183-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
184            </intent-filter>
185            <intent-filter>
185-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
186                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
186-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
186-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
187            </intent-filter>
188            <intent-filter>
188-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
189                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
189-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
189-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.12\transforms\a41c77be64ae79775eea5abf0296f1bb\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
190            </intent-filter>
191        </receiver>
192    </application>
193
194</manifest>
