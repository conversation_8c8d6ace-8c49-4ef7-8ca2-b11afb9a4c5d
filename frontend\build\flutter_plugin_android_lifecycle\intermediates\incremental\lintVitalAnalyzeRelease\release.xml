<variant
    name="release"
    package="io.flutter.plugins.flutter_plugin_android_lifecycle"
    minSdkVersion="21"
    targetSdkVersion="21"
    mergedManifest="D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_plugin_android_lifecycle\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml"
    proguardFiles="D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_plugin_android_lifecycle\intermediates\default_proguard_files\global\proguard-android.txt-8.7.3"
    consumerProguardFiles="proguard.txt"
    partialResultsDir="D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_plugin_android_lifecycle\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.12\transforms\7ff7c222f72a39cd2d6660f4156e5aff\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_plugin_android_lifecycle\intermediates\javac\release\compileReleaseJavaWithJavac\classes;D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_plugin_android_lifecycle\intermediates\compile_r_class_jar\release\generateReleaseRFile\R.jar"
      type="MAIN"
      applicationId="io.flutter.plugins.flutter_plugin_android_lifecycle"
      generatedSourceFolders="D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_plugin_android_lifecycle\generated\ap_generated_sources\release\out"
      generatedResourceFolders="D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_plugin_android_lifecycle\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.12\transforms\7ff7c222f72a39cd2d6660f4156e5aff\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
