<lint-module
    format="1"
    dir="C:\Users\<USER>\AppData\Local\Pub\Cache\hosted\pub.dev\geolocator_android-4.6.2\android"
    name=":geolocator_android"
    type="LIBRARY"
    maven="com.baseflow.geolocator:geolocator_android:1.0"
    agpVersion="8.7.3"
    buildFolder="D:\workspaces\nsl\back\srsrmain\frontend\build\geolocator_android"
    bootClassPath="C:\Users\<USER>\AppData\Local\Android\sdk\platforms\android-35\android.jar;C:\Users\<USER>\AppData\Local\Android\sdk\build-tools\34.0.0\core-lambda-stubs.jar"
    javaSourceLevel="1.8"
    compileTarget="android-35"
    neverShrinking="true">
  <lintOptions
      disable="InvalidPackage"
      abortOnError="true"
      absolutePaths="true"
      checkReleaseBuilds="true"
      explainIssues="true">
    <severities>
      <severity
        id="InvalidPackage"
        severity="IGNORE" />
    </severities>
  </lintOptions>
  <variant name="release"/>
</lint-module>
