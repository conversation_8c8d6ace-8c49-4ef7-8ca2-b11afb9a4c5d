<variant
    name="release"
    package="id.flutter.flutter_background_service"
    minSdkVersion="16"
    targetSdkVersion="16"
    mergedManifest="D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml"
    manifestMergeReport="D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\outputs\logs\manifest-merger-release-report.txt"
    proguardFiles="D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\default_proguard_files\global\proguard-android.txt-8.7.3"
    consumerProguardFiles="proguard-rules.pro"
    partialResultsDir="D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\lint_partial_results\release\lintAnalyzeRelease\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\javac\release\compileReleaseJavaWithJavac\classes;D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\intermediates\compile_r_class_jar\release\generateReleaseRFile\R.jar"
      type="MAIN"
      applicationId="id.flutter.flutter_background_service"
      generatedSourceFolders="D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\generated\ap_generated_sources\release\out"
      generatedResourceFolders="D:\workspaces\nsl\back\srsrmain\frontend\build\flutter_background_service_android\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.12\transforms\0ba2b84671a68b7a206f6fa8e3f854a9\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
