<variant
    name="release"
    package="com.mr.flutter.plugin.filepicker"
    minSdkVersion="21"
    targetSdkVersion="21"
    mergedManifest="D:\workspaces\nsl\back\srsrmain\frontend\build\file_picker\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml"
    manifestMergeReport="D:\workspaces\nsl\back\srsrmain\frontend\build\file_picker\outputs\logs\manifest-merger-release-report.txt"
    proguardFiles="D:\workspaces\nsl\back\srsrmain\frontend\build\file_picker\intermediates\default_proguard_files\global\proguard-android.txt-8.7.3"
    partialResultsDir="D:\workspaces\nsl\back\srsrmain\frontend\build\file_picker\intermediates\lint_partial_results\release\lintAnalyzeRelease\out">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="D:\workspaces\nsl\back\srsrmain\frontend\build\file_picker\intermediates\javac\release\compileReleaseJavaWithJavac\classes;D:\workspaces\nsl\back\srsrmain\frontend\build\file_picker\intermediates\compile_r_class_jar\release\generateReleaseRFile\R.jar"
      type="MAIN"
      applicationId="com.mr.flutter.plugin.filepicker"
      generatedSourceFolders="D:\workspaces\nsl\back\srsrmain\frontend\build\file_picker\generated\ap_generated_sources\release\out"
      generatedResourceFolders="D:\workspaces\nsl\back\srsrmain\frontend\build\file_picker\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.12\transforms\7ff7c222f72a39cd2d6660f4156e5aff\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
