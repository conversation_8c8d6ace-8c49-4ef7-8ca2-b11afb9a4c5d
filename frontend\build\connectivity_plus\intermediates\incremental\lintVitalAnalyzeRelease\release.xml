<variant
    name="release"
    package="dev.fluttercommunity.plus.connectivity"
    minSdkVersion="19"
    targetSdkVersion="19"
    mergedManifest="D:\workspaces\nsl\back\srsrmain\frontend\build\connectivity_plus\intermediates\merged_manifest\release\processReleaseManifest\AndroidManifest.xml"
    proguardFiles="D:\workspaces\nsl\back\srsrmain\frontend\build\connectivity_plus\intermediates\default_proguard_files\global\proguard-android.txt-8.7.3"
    partialResultsDir="D:\workspaces\nsl\back\srsrmain\frontend\build\connectivity_plus\intermediates\lint_vital_partial_results\release\lintVitalAnalyzeRelease\out"
    desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.12\transforms\a7a9bf31db10813def01c228e81c73cc\transformed\D8BackportedDesugaredMethods.txt">
  <buildFeatures
      namespacing="REQUIRED"/>
  <sourceProviders>
    <sourceProvider
        manifests="src\main\AndroidManifest.xml"
        javaDirectories="src\main\java;src\release\java;src\main\kotlin;src\release\kotlin"
        resDirectories="src\main\res;src\release\res"
        assetsDirectories="src\main\assets;src\release\assets"/>
  </sourceProviders>
  <testSourceProviders>
  </testSourceProviders>
  <testFixturesSourceProviders>
  </testFixturesSourceProviders>
  <artifact
      classOutputs="D:\workspaces\nsl\back\srsrmain\frontend\build\connectivity_plus\intermediates\javac\release\compileReleaseJavaWithJavac\classes;D:\workspaces\nsl\back\srsrmain\frontend\build\connectivity_plus\intermediates\compile_r_class_jar\release\generateReleaseRFile\R.jar"
      type="MAIN"
      applicationId="dev.fluttercommunity.plus.connectivity"
      generatedSourceFolders="D:\workspaces\nsl\back\srsrmain\frontend\build\connectivity_plus\generated\ap_generated_sources\release\out"
      generatedResourceFolders="D:\workspaces\nsl\back\srsrmain\frontend\build\connectivity_plus\generated\res\resValues\release"
      desugaredMethodsFiles="C:\Users\<USER>\.gradle\caches\8.12\transforms\a7a9bf31db10813def01c228e81c73cc\transformed\D8BackportedDesugaredMethods.txt">
  </artifact>
</variant>
