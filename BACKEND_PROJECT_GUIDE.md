# SRSR Property Management Backend - Complete Guide

## 🏗️ **Project Overview**

The SRSR Property Management Backend is a comprehensive **Node.js/Express.js** application built with **TypeScript** that serves as the API backend for a Flutter mobile application. It manages multiple properties, offices, and construction sites with sophisticated **Role-Based Access Control (RBAC)** and real-time monitoring capabilities.

## 🛠️ **Technology Stack**

### **Core Technologies**
- **Runtime**: Node.js 18+
- **Framework**: Express.js with TypeScript
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: JWT (JSON Web Tokens) with bcrypt
- **Validation**: Zod schema validation
- **Documentation**: Swagger/OpenAPI 3.0
- **Security**: Helmet, CORS, Rate Limiting
- **File Processing**: Multer for uploads, Sharp for image processing
- **Development**: Nodemon, ESLint, TypeScript compiler

### **Key Dependencies**
```json
{
  "express": "^4.18.2",           // Web framework
  "@prisma/client": "^5.7.0",     // Database ORM
  "jsonwebtoken": "^9.0.2",       // JWT authentication
  "bcryptjs": "^2.4.3",           // Password hashing
  "zod": "^3.22.4",               // Schema validation
  "swagger-jsdoc": "^6.2.8",      // API documentation
  "helmet": "^7.1.0",             // Security middleware
  "cors": "^2.8.5",               // Cross-origin requests
  "rate-limiter-flexible": "^2.4.2" // Rate limiting
}
```

## 📁 **Project Structure**

```
backend/
├── src/
│   ├── config/           # RBAC and permission configurations
│   ├── controllers/      # Business logic handlers
│   ├── lib/             # Core utilities (auth, prisma, validation)
│   ├── middleware/      # Express middleware (auth, validation, errors)
│   ├── models/          # Data models (some legacy JS files)
│   ├── routes/          # API route definitions
│   ├── services/        # Business services
│   ├── types/           # TypeScript type definitions
│   └── server.ts        # Main application entry point
├── prisma/
│   ├── schema.prisma    # Database schema definition
│   ├── seed.ts          # Database seeding script
│   └── migrations/      # Database migration files
├── uploads/             # File upload storage
├── dist/                # Compiled TypeScript output
└── package.json         # Dependencies and scripts
```

## 🗄️ **Database Architecture**

### **Core Entities**

#### **1. User Management**
```typescript
// Users with role-based access
User {
  id: string (UUID)
  name: string
  email: string (unique)
  phone: string
  password: string (hashed)
  role: UserRole (enum)
  isActive: boolean
  assignedProperties: UserProperty[]
}

// User roles enum
enum UserRole {
  SUPER_ADMIN           // Full system access
  PROPERTY_MANAGER      // Property management
  OFFICE_MANAGER        // Office/attendance management
  SECURITY_PERSONNEL    // Security systems only
  MAINTENANCE_STAFF     // Maintenance tasks only
  CONSTRUCTION_SUPERVISOR // Construction sites
}
```

#### **2. Property Management**
```typescript
Property {
  id: string
  name: string
  type: PropertyType (RESIDENTIAL | OFFICE | CONSTRUCTION)
  address: string
  description: string
  isActive: boolean
  latitude?: float
  longitude?: float
  images: string[]
  systemConfigs: PropertySystemConfig[]
  systemContents: SystemContent[]
}

// System types available per property
enum SystemType {
  WATER         // Water management
  ELECTRICITY   // Power systems
  SECURITY      // CCTV, access control
  INTERNET      // Network connectivity
  OTT           // Streaming services
  MAINTENANCE   // Maintenance tasks
}
```

#### **3. System Monitoring**
```typescript
SystemStatus {
  propertyId: string
  systemType: SystemType
  status: SystemStatusEnum (OPERATIONAL | WARNING | CRITICAL | OFFLINE)
  description?: string
  healthScore?: float (0-100)
  lastChecked: DateTime
}

// Detailed system monitoring
WaterSystem {
  propertyId: string
  tankName: string
  capacity: float
  currentLevel: float
  levelPercentage: float
  pumpStatus: string
  flowRate?: float
  pressure?: float
  quality?: string
}

ElectricitySystem {
  propertyId: string
  systemName: string
  generatorStatus: string
  fuelLevel?: float
  powerConsumption?: float
  voltage?: float
  mainsPowerStatus: string
  batteryBackup?: float
}
```

#### **4. Office & Employee Management**
```typescript
Office {
  id: string
  name: string
  type: OfficeType (OFFICE | CONSTRUCTION_SITE)
  address: string
  isActive: boolean
  workingHours: Json
  employees: Employee[]
}

Employee {
  id: string
  officeId: string
  name: string
  email?: string
  phone?: string
  employeeId: string (unique)
  designation: string
  salary?: float
  isActive: boolean
  joinDate: DateTime
}

AttendanceRecord {
  officeId: string
  employeeId?: string
  userId?: string
  date: DateTime
  status: AttendanceStatus
  checkInTime?: DateTime
  checkOutTime?: DateTime
  hoursWorked?: float
  overtime?: float
}
```

## 🔐 **Authentication & Authorization System**

### **JWT Token-Based Authentication**

#### **Token Structure**
```typescript
interface JWTPayload {
  userId: string
  email: string
  role: UserRole
  assignedProperties: string[]
  iat?: number  // Issued at
  exp?: number  // Expires at
}

// Token types
AccessToken: 15 minutes lifespan
RefreshToken: 7 days lifespan
```

#### **Authentication Flow**
1. **Login**: User provides email/password
2. **Verification**: Password checked against bcrypt hash
3. **Token Generation**: Access + Refresh tokens created
4. **Token Storage**: Refresh token stored in database
5. **API Access**: Access token sent in Authorization header
6. **Token Refresh**: Use refresh token to get new access token

### **Role-Based Access Control (RBAC)**

#### **Permission Matrix**
| Feature | Super Admin | Property Manager | Office Manager | Security | Maintenance | Construction |
|---------|-------------|------------------|----------------|----------|-------------|--------------|
| Dashboard | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |
| Properties | ✅ | ✅ (assigned) | ❌ | ❌ | ❌ | ❌ |
| Office Management | ✅ | ❌ | ✅ | ❌ | ❌ | ✅ (sites) |
| User Management | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| Security Systems | ✅ | ✅ | ❌ | ✅ | ❌ | ❌ |
| Maintenance | ✅ | ✅ | ❌ | ❌ | ✅ | ❌ |
| Reports | ✅ | ✅ | ✅ | ❌ | ❌ | ✅ |

#### **Granular Permissions**
The system implements **breadcrumb-based permissions** with path-level access control:

```typescript
// Example: Security personnel accessing CCTV
{
  path: '/properties/{propertyId}/systems/security/cctv',
  role: 'SECURITY_PERSONNEL',
  accessLevel: 'restricted',
  permissions: ['view', 'view_live_feeds'],
  restrictions: {
    hideComponents: ['security.cctv.add_camera', 'security.cctv.settings'],
    customConditions: {
      recordingRetentionDays: 7,
      canViewLiveFeeds: true,
      canControlPTZ: false
    }
  }
}
```

## 🛣️ **API Architecture**

### **RESTful API Design**
Base URL: `http://localhost:3000/v1`

#### **Core Endpoints**

**Authentication**
```bash
POST /v1/auth/login          # User login
POST /v1/auth/refresh        # Token refresh
GET  /v1/auth/me            # Current user info
POST /v1/auth/logout        # User logout
```

**Properties**
```bash
GET    /v1/properties                    # List properties
GET    /v1/properties/{id}               # Property details
PUT    /v1/properties/{id}/systems/{type} # Update system status
GET    /v1/properties/{id}/systems       # Get all systems
```

**Dashboard**
```bash
GET /v1/dashboard/overview    # Dashboard metrics
GET /v1/dashboard/alerts      # System alerts
GET /v1/dashboard/activities  # Recent activities
```

**Office Management**
```bash
GET    /v1/offices                      # List offices
POST   /v1/offices/{id}/attendance      # Submit attendance
GET    /v1/offices/{id}/employees       # Office employees
```

### **Request/Response Format**

#### **Standard API Response**
```typescript
interface ApiResponse<T> {
  success: boolean
  data?: T
  message?: string
  error?: string
  timestamp: string
  path?: string
}

// Success response
{
  "success": true,
  "data": { ... },
  "message": "Operation completed successfully",
  "timestamp": "2024-01-15T10:30:00Z"
}

// Error response
{
  "success": false,
  "error": "VALIDATION_ERROR",
  "message": "Invalid input data",
  "timestamp": "2024-01-15T10:30:00Z",
  "path": "/properties"
}
```

#### **Pagination**
```typescript
interface PaginatedResponse<T> {
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
    hasNext: boolean
    hasPrev: boolean
  }
}
```

## 🔧 **Middleware Stack**

### **Security Middleware**
1. **Helmet**: Security headers
2. **CORS**: Cross-origin resource sharing
3. **Rate Limiting**: Request throttling
4. **Authentication**: JWT verification
5. **Authorization**: Role-based access control

### **Request Processing**
1. **Body Parsing**: JSON/URL-encoded data
2. **Validation**: Zod schema validation
3. **Compression**: Response compression
4. **Logging**: Morgan HTTP logging
5. **Error Handling**: Centralized error management

## 📊 **System Features**

### **Real-time Monitoring**
- **System Health**: Water, electricity, security status
- **Alerts**: Critical system notifications
- **Dashboard Metrics**: Live property statistics
- **Activity Logging**: User action tracking

### **Property Management**
- **Multi-property Support**: Residential, office, construction
- **System Configuration**: Enable/disable systems per property
- **Content Management**: Rich text content for each system
- **Contact Management**: Property-specific contacts

### **Office Operations**
- **Employee Management**: Staff information and roles
- **Attendance Tracking**: Daily check-in/check-out
- **Construction Sites**: Worker attendance and progress
- **Reporting**: Attendance and performance reports

### **Maintenance System**
- **Issue Tracking**: Maintenance requests and status
- **Recurring Tasks**: Scheduled maintenance
- **Department Management**: Organized by departments
- **Work Orders**: Assignment and tracking

## 🚀 **Development Workflow**

### **Environment Setup**
```bash
# Install dependencies
npm install

# Database setup
npm run db:generate    # Generate Prisma client
npm run db:push       # Push schema to database
npm run db:seed       # Seed with sample data

# Development
npm run dev           # Start development server
npm run build         # Build for production
npm start            # Start production server
```

### **Environment Variables**
```env
# Database
DATABASE_URL="postgresql://user:pass@localhost:5432/srsr_db"

# JWT Secrets
JWT_SECRET="your-secret-key"
JWT_REFRESH_SECRET="your-refresh-secret"
JWT_EXPIRES_IN="15m"
JWT_REFRESH_EXPIRES_IN="7d"

# Server
NODE_ENV="development"
PORT=3000
API_VERSION="v1"

# Security
CORS_ORIGIN="http://localhost:3000"
RATE_LIMIT_MAX_REQUESTS=100
```

### **Available Scripts**
```json
{
  "dev": "nodemon src/server.ts",
  "build": "tsc",
  "start": "node dist/server.js",
  "db:generate": "prisma generate",
  "db:push": "prisma db push",
  "db:migrate": "prisma migrate dev",
  "db:studio": "prisma studio",
  "db:seed": "tsx prisma/seed.ts",
  "lint": "eslint src/**/*.ts",
  "type-check": "tsc --noEmit"
}
```

## 📈 **Monitoring & Health Checks**

### **Health Endpoints**
```bash
GET /health           # Basic health check
GET /health/detailed  # Comprehensive system status
```

### **Health Check Response**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-15T10:30:00Z",
  "uptime": 3600,
  "version": "1.0.0",
  "services": {
    "database": {
      "status": "healthy",
      "responseTime": 15,
      "lastCheck": "2024-01-15T10:30:00Z"
    }
  },
  "metrics": {
    "memoryUsage": 65,
    "cpuUsage": 0,
    "activeConnections": 0
  }
}
```

### **Metrics Tracked**
- Database connection status
- Memory usage
- Response times
- Error rates
- Active connections

## 🔒 **Security Features**

### **Data Protection**
- **Password Hashing**: bcrypt with configurable rounds
- **JWT Encryption**: Secure token generation
- **Input Validation**: Zod schema validation
- **SQL Injection Prevention**: Prisma ORM protection
- **XSS Protection**: Helmet security headers

### **Rate Limiting**
```typescript
// Different limits by endpoint type
Authentication: 5 requests/minute
Read Operations: 100 requests/minute
Write Operations: 30 requests/minute
File Uploads: 10 requests/minute
```

### **CORS Configuration**
```typescript
const corsOptions = {
  origin: function (origin, callback) {
    const allowedOrigins = [
      'http://localhost:3000',
      'http://localhost:3001',
      'http://localhost:8080'
    ];

    if (!origin || allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      callback(new Error('Not allowed by CORS'));
    }
  },
  credentials: true,
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS']
};
```

## 🐳 **Deployment**

### **Docker Support**
```dockerfile
# Multi-stage build
FROM node:18-alpine AS base

# Install dependencies
FROM base AS deps
WORKDIR /app
COPY package.json package-lock.json* ./
RUN npm ci --only=production

# Build application
FROM base AS builder
WORKDIR /app
COPY --from=deps /app/node_modules ./node_modules
COPY . .
RUN npx prisma generate
RUN npm run build

# Production image
FROM base AS runner
WORKDIR /app
ENV NODE_ENV production

# Copy built application
COPY --from=builder /app/dist ./dist
COPY --from=builder /app/package.json ./package.json
COPY --from=builder /app/prisma ./prisma
COPY --from=builder /app/node_modules ./node_modules

EXPOSE 3000
CMD ["node", "dist/server.js"]
```

### **Production Considerations**
- **Environment Variables**: Secure configuration
- **Database Migrations**: Automated schema updates
- **Health Checks**: Container health monitoring
- **Logging**: Structured logging for production
- **Monitoring**: Performance and error tracking

## 📚 **API Documentation**

### **Swagger/OpenAPI 3.0**
The system includes comprehensive API documentation available at:
- **Development**: `http://localhost:3000/api-docs`
- **Interactive Testing**: Built-in API explorer
- **Schema Definitions**: Complete request/response schemas

### **Documentation Features**
- **Authentication**: Bearer token support
- **Request Examples**: Sample requests for all endpoints
- **Response Schemas**: Detailed response structures
- **Error Codes**: Complete error documentation
- **Rate Limiting**: Request limit information

## 🎯 **Key Business Logic**

### **Property-Based Access Control**
Users can only access properties they're assigned to (except Super Admin):

```typescript
// Property access check in middleware
export const requirePropertyAccess = (req, res, next) => {
  const user = req.user;
  const propertyId = req.params.propertyId;

  if (user.role !== UserRole.SUPER_ADMIN &&
      !user.assignedProperties.includes(propertyId)) {
    return res.status(403).json({
      success: false,
      error: 'FORBIDDEN',
      message: 'Access denied to this property'
    });
  }

  next();
};
```

### **System Content Management**
Admins can manage rich content for each system:

```typescript
SystemContent {
  propertyId: string
  systemType: SystemType
  contentType: string  // 'contact', 'maintenance_task', 'rich_content'
  title: string
  content: Json        // Flexible content storage
  richContent?: string // Large text content (Markdown/HTML)
  contentFormat?: string // 'markdown', 'html', 'json'
  isTab: boolean      // Tab-level content
  tabIcon?: string    // Icon for tab
  displayOrder?: int  // Content ordering
}
```

### **Dynamic System Configuration**
Properties can have different systems enabled:

```typescript
PropertySystemConfig {
  propertyId: string
  systemType: SystemType
  isEnabled: boolean
  displayName?: string    // Custom system name
  displayOrder?: int      // System ordering
  configuration: Json     // System-specific config
}
```

## 🔄 **Data Flow Architecture**

### **Request Lifecycle**
1. **Client Request**: Mobile app sends HTTP request
2. **Rate Limiting**: Check request limits
3. **CORS Validation**: Verify origin
4. **Authentication**: Validate JWT token
5. **Authorization**: Check role permissions
6. **Validation**: Validate request data
7. **Business Logic**: Execute controller logic
8. **Database Query**: Prisma ORM operations
9. **Response**: Send formatted response

### **Authentication Flow**
```mermaid
sequenceDiagram
    participant Client
    participant API
    participant Database

    Client->>API: POST /auth/login
    API->>Database: Verify user credentials
    Database-->>API: User data
    API->>API: Generate JWT tokens
    API->>Database: Store refresh token
    API-->>Client: Access + Refresh tokens

    Client->>API: API request with Bearer token
    API->>API: Verify access token
    API->>Database: Check user status
    API-->>Client: Protected resource
```

## 🧪 **Testing Strategy**

### **Test Types**
- **Unit Tests**: Individual function testing
- **Integration Tests**: API endpoint testing
- **Security Tests**: Authentication and authorization
- **Performance Tests**: Load and stress testing

### **Test Commands**
```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run specific test file
npm test -- auth.test.ts

# Run integration tests
npm run test:integration
```

## 📋 **Common Use Cases**

### **1. User Login**
```typescript
// POST /v1/auth/login
{
  "email": "<EMAIL>",
  "password": "admin123",
  "deviceId": "device-123"
}

// Response
{
  "success": true,
  "data": {
    "user": {
      "id": "user-123",
      "name": "Admin User",
      "email": "<EMAIL>",
      "role": "SUPER_ADMIN",
      "assignedProperties": ["prop-1", "prop-2"]
    },
    "token": {
      "accessToken": "eyJ...",
      "refreshToken": "eyJ...",
      "expiresAt": "2024-01-15T11:00:00Z",
      "tokenType": "Bearer"
    }
  }
}
```

### **2. Get Property Systems**
```typescript
// GET /v1/properties/prop-123/systems
// Headers: Authorization: Bearer eyJ...

// Response
{
  "success": true,
  "data": [
    {
      "systemType": "WATER",
      "status": "OPERATIONAL",
      "healthScore": 95,
      "lastChecked": "2024-01-15T10:30:00Z",
      "description": "All systems normal"
    },
    {
      "systemType": "ELECTRICITY",
      "status": "WARNING",
      "healthScore": 75,
      "lastChecked": "2024-01-15T10:25:00Z",
      "description": "Generator fuel low"
    }
  ]
}
```

### **3. Submit Attendance**
```typescript
// POST /v1/offices/office-123/attendance
{
  "date": "2024-01-15",
  "records": [
    {
      "employeeId": "emp-123",
      "status": "PRESENT",
      "checkInTime": "09:15:00",
      "checkOutTime": "18:45:00",
      "hoursWorked": 9.5,
      "overtime": 0.5
    }
  ]
}
```

## 🚨 **Error Handling**

### **Error Types**
```typescript
enum ErrorCodes {
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  NOT_FOUND = 'NOT_FOUND',
  RATE_LIMITED = 'RATE_LIMITED',
  INTERNAL_SERVER_ERROR = 'INTERNAL_SERVER_ERROR'
}
```

### **Error Response Format**
```typescript
{
  "success": false,
  "error": "VALIDATION_ERROR",
  "message": "Invalid input data",
  "timestamp": "2024-01-15T10:30:00Z",
  "path": "/properties",
  "details": {
    "field": "name",
    "issue": "required"
  }
}
```

## 🔧 **Configuration Management**

### **Environment-Specific Settings**
```typescript
// Development
NODE_ENV=development
PORT=3000
DATABASE_URL="postgresql://localhost:5432/srsr_dev"
JWT_SECRET="dev-secret"

// Production
NODE_ENV=production
PORT=3000
DATABASE_URL="postgresql://prod-server:5432/srsr_prod"
JWT_SECRET="super-secure-production-secret"
```

### **Feature Flags**
```typescript
// Environment variables for feature control
SWAGGER_ENABLED=true
RATE_LIMITING_ENABLED=true
FILE_UPLOAD_ENABLED=true
WEBSOCKET_ENABLED=false
```

## 📊 **Performance Optimization**

### **Database Optimization**
- **Indexing**: Strategic database indexes
- **Query Optimization**: Efficient Prisma queries
- **Connection Pooling**: Database connection management
- **Caching**: Redis for frequently accessed data

### **API Optimization**
- **Compression**: Gzip response compression
- **Pagination**: Limit large dataset responses
- **Field Selection**: Return only requested fields
- **Caching Headers**: HTTP caching strategies

## 🎓 **Getting Started for New Developers**

### **Prerequisites**
1. **Node.js 18+**: JavaScript runtime
2. **PostgreSQL 12+**: Database server
3. **Git**: Version control
4. **VS Code**: Recommended IDE
5. **Postman**: API testing tool

### **Setup Steps**
```bash
# 1. Clone repository
git clone <repository-url>
cd backend

# 2. Install dependencies
npm install

# 3. Setup environment
cp .env.example .env
# Edit .env with your database credentials

# 4. Setup database
npm run db:generate
npm run db:push
npm run db:seed

# 5. Start development server
npm run dev

# 6. Test API
curl http://localhost:3000/health
```

### **Development Tips**
1. **Use TypeScript**: Leverage type safety
2. **Follow Conventions**: Consistent code style
3. **Write Tests**: Test your code
4. **Use Prisma Studio**: Visual database management
5. **Check Swagger Docs**: API documentation reference

## 🤝 **Contributing Guidelines**

### **Code Standards**
- **TypeScript**: Strict type checking
- **ESLint**: Code linting rules
- **Prettier**: Code formatting
- **Conventional Commits**: Commit message format

### **Pull Request Process**
1. Create feature branch
2. Write tests for new features
3. Ensure all tests pass
4. Update documentation
5. Submit pull request

## 📞 **Support & Resources**

### **Documentation**
- **API Docs**: `http://localhost:3000/api-docs`
- **Database Schema**: `prisma/schema.prisma`
- **Environment Setup**: `.env.example`

### **Troubleshooting**
- **Database Issues**: Check connection string
- **Authentication Errors**: Verify JWT secrets
- **CORS Problems**: Check allowed origins
- **Rate Limiting**: Check request limits

---

This backend provides a robust, scalable foundation for the SRSR Property Management system with comprehensive RBAC, real-time monitoring, and multi-property management capabilities. The architecture supports both current requirements and future expansion needs.
```
